<template>
  <div>
    <div class="chart-container">
      <div class="chart-container1">
        <h2>应用商店排行</h2>
        <a-button
          class="custom-button"
          :class="{ selected: selectedType === 'top_free' }"
          @click="selectedType = 'top_free'"
        >免费下载</a-button>
        <a-button
          class="custom-button"
          :class="{ selected: selectedType === 'top_paid' }"
          @click="selectedType = 'top_paid'"
        >付费下载</a-button>
        <a-button
          class="custom-button"
          :class="{ selected: selectedType === 'top_grossing' }"
          @click="selectedType = 'top_grossing'"
        >净收入</a-button>
      </div>

      <div class="chart-container1">
        <!-- 日期选择 -->
        <a-space direction="vertical" :size="12" style="margin-left: 2%;">
          <!-- <a-range-picker :presets="rangePresets" v-model:value="selectedRange" /> -->
          <datePicker v-model:value="selectedRange" style="width: 15vw;height: 30px;"></datePicker>
        </a-space>

        <!-- 国家选择 -->
        <a-select
          v-model:value="selectedCountry"
          mode="multiple"
          allowClear
          placeholder="请选择国家"
          style="width: 15vw;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange"
        >
          <a-select-option value="all">选择全部</a-select-option>
          <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
        </a-select>

        <!-- 平台选择 -->
        <a-select
          v-model:value="selectedDevice"
          mode="multiple"
          allowClear
          placeholder="全部平台"
          style="width: 15vw;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleDeviceChange"
        >
          <a-select-option value="all">选择全部</a-select-option>
          <a-select-option v-for="device in devices" :key="device.value" :value="device.value">
            {{ device.label }}
          </a-select-option>
        </a-select>

        <!-- 查询按钮 -->
        <a-button type="primary" @click="handleQuery" style="margin-left: 20px;">查询</a-button>
      </div>

      <div style="position: relative;">
        <div ref="chartRef" style="width: 100%; height: 400px; margin: auto;"></div>
        <div
          v-if="!hasChartData"
          class="empty-data-container"
          style="
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            background: #fff;
            border-radius: 4px;
          "
        >
          <i class="empty-icon">🔍</i>
          <p>没有找到匹配的数据</p>
          <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <div class="chart-container">
      <h2>游戏活跃度分析</h2>
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="{
          current: currentPage,
          pageSize: pageSize,
          total: totalGames,
          onChange: (page, newPageSize) => {
            currentPage = page;
            pageSize = newPageSize;
            fetchGameActivityData();
          },
          showSizeChanger: true,
          pageSizeOptions: ['5', '10', '20']
        }"
        :scroll="{x: 'max-content'}"
        size="middle"
        :bordered="true"
        :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        class="custom-table"
      >
        <!-- 应用信息列 -->
        <template #gameInfo="{ record }">
          <div style="display: flex; align-items: center; gap: 8px;">
            <img 
              :src="record.iconUrl" 
              style="width: 40px; height: 40px; border-radius: 4px;" 
              alt="游戏图标"
            />
            <span>{{ record.gameName || '-' }}</span>
          </div>
        </template>
        
        <!-- 涨幅数据列 -->
        <template #trendCell="{ text }">
          <span v-if="text" :style="{ 
            color: parseFloat(text.replace('%', '')) >= 0 ? '#52c41a' : '#f5222d' 
          }">
            {{ text }}
          </span>
          <span v-else>-</span>
        </template>

        <template #emptyText>
          <div class="empty-data-container">
            <i class="empty-icon">🔍</i>
            <p>没有找到匹配的数据</p>
            <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 全球热力图容器 -->
    <div class="chart-container" style="margin-top: 20px;">
      <div class="chart-containertitle">
        <h2>全球热度分布</h2>
      </div>
      <!-- 筛选条件区域 -->
      <div class="filter-container" style="margin-bottom: 20px; display: flex; align-items: center; gap: 16px;">

        <!-- 游戏选择 -->
        <div style="display: flex; align-items: center; gap: 8px;">
          <a-select
            v-model:value="selectedGame"
            show-search
            placeholder="请选择游戏"
            style="width: 200px"
            :default-active-first-option="false"
            :show-arrow="true"
            :filter-option="false"
            :not-found-content="gameLoading ? '加载中...' : '未找到'"
            @search="onGameSearch"
            @popupScroll="onGamePopupScroll"
          >
            <a-select-option v-for="game in gameOptions" :key="game.id" :value="game.id">
              <div style="display: flex; align-items: center; gap: 8px;">
                <img :src="game.iconUrl" style="width: 24px; height: 24px; border-radius: 4px;" />
                <span>{{ game.nameZh }}</span>
              </div>
            </a-select-option>
          </a-select>

          <!-- 已选游戏展示 -->
          <div v-if="selectedGameInfo" style="display: flex; align-items: center; gap: 8px; padding: 4px 8px; background: #f5f5f5; border-radius: 4px;">
            <img :src="selectedGameInfo.iconUrl" style="width: 24px; height: 24px; border-radius: 4px;" />
            <span>{{ selectedGameInfo.nameZh }}</span>
          </div>

        </div>

        <!-- 日期选择 -->
        <!-- <a-range-picker
          v-model:value="heatmapDateRange"
          :presets="rangePresets"
          style="width: 260px;"
        /> -->

        <datePicker v-model:value="heatmapDateRange" style="width: 15vw;height: 30px;"></datePicker>

        <!-- 国家选择 -->
        <a-select
          v-model:value="selectedCountry1"
          mode="multiple"
          allowClear
          placeholder="请选择国家"
          style="width: 15vw;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange1"
        >
          <a-select-option value="all">选择全部</a-select-option>
          <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
        </a-select>

        <!-- 展示数据选择 -->
        <a-select
          v-model:value="selectedDataType"
          placeholder="请选择展示数据"
          style="width: 200px"
        >
          <a-select-option value="下载量">下载量</a-select-option>
          <a-select-option value="收入">收入</a-select-option>
          <!-- <a-select-option value="active">活跃度</a-select-option> -->
        </a-select>

        <!-- 查询按钮 -->
        <a-button type="primary" @click="fetchHeatmapData">查询</a-button>
      </div>

      <div ref="heatmapRef" style="width: 100%; height: 400px;"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, h } from 'vue';
import * as echarts from 'echarts';
import dayjs, { Dayjs } from 'dayjs';
import type { TreeSelectProps } from 'ant-design-vue';
import { TreeSelect } from 'ant-design-vue';
import type { CascaderProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import mihayoImg from '@/views/release-scan/download-analysis/image/mihayo.png';
import { BasicTable, useTable } from '/@/components/Table';
import { message } from 'ant-design-vue';
import { defHttp } from '/@/utils/http/axios';
import { findGamesByPrefixApi } from '@/api/public-opinion-monitoring';
import worldJson from './world.json';
import { 
  topApi,
  getAllDeviceApi,
  getAllCountryApi,
  gameActivityAnalysisApi,
  globalHeatApi,
} from '@/api/release-scan/channel-performance';
import { 
  saveFilterSettingsApi,
  getFilterSettingsApi
} from '/@/api/store-information/market-dynamics/popular-ranking';
import { useUserStore } from '/@/store/modules/user';
import datePicker from '/@/views/public-opinion-monitoring/components/datePicker.vue';

// 国家名称的映射
import isoCountries from 'i18n-iso-countries'
import zh from 'i18n-iso-countries/langs/zh.json'
import en from 'i18n-iso-countries/langs/en.json'
import { AsyncLocalStorage } from 'node:async_hooks';

isoCountries.registerLocale(zh)
isoCountries.registerLocale(en)


// 注册世界地图数据
echarts.registerMap('world', worldJson as { type: 'FeatureCollection', features: any[] });

// 查询类型
const selectedType = ref<'top_free' | 'top_paid' | 'top_grossing'>('top_free');

// 日期、国家、平台
const selectedRange = ref<RangeValue>([dayjs().add(-1, 'year'), dayjs()]);

const selectedCountry = ref<string[]>([]);
const selectedCountry1 = ref<string[]>([]);
const selectedDevice = ref<string[]>([]);
const countries = ref<{ value: string; label: string }[]>([]);
const devices = ref<{ value: string; label: string }[]>([]);

// 获取输入框平台类型
const fetchDevices = async () => {
  try {
    const res = await getAllDeviceApi();

    // 处理返回值，将apple转换为App Store，并添加"选择全部"选项
    const deviceOptions = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value,
    }));
    
    // 添加"选择全部"选项
    devices.value = [
      ...deviceOptions,
    ];
  } catch (e) {
    devices.value = [
    ];
  }
};

// 获取输入框国家类型
const fetchCountriesGenres = async () => {
  try {
    const res = await getAllCountryApi()
    // 下拉选项保持原样
    countries.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value,
    }))
    // 动态生成中文→英文映射
    countryNameMap.value = (res || []).reduce((map: Record<string,string>, item: any) => {
      const cn = item.value
      // 跳过纯数字
      if (/^\d+$/.test(cn)) return map
      // 拿 ISO2
      const iso2 = isoCountries.getAlpha2Code(cn, 'zh');
      let enName = iso2 ? isoCountries.getName(iso2, 'en') : undefined;
      if (enName === "People's Republic of China") {
        enName = 'China';
      }
      if (!enName) {
        enName = manualCountryMap[cn];
      }
      map[cn] = enName || cn;
      return map
    }, {})
    
  } catch (e) {
    countries.value = []
    countryNameMap.value = {}
  }
}


onMounted(async() => {
  fetchDevices();
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
  }

  // 恢复筛选条件
  const hasRestored = await restoreFilterSettings();
  const hasRestoredHot = await restoreFilterSettingsHot();
  
  // 如果恢复成功，则执行查询
  if (hasRestored) {
    handleQuery();
  }

  if (hasRestoredHot) {
    fetchHeatmapData();
  }
  
  // 初始化热力图
  initHeatmap();
  loadGameOptions();
  fetchCountriesGenres();

  // 初始加载游戏活跃度数据
  // fetchGameActivityData();
});

// 表格
const columns = ref([
  {
    title: '排名',
    dataIndex: 'ranking',
    key: 'ranking',
    width: 80,
    align: 'center',
  },
  {
    title: '应用',
    dataIndex: 'gameName',
    key: 'gameName',
    // 使用名为gameInfo的插槽自定义渲染
    slots: { customRender: 'gameInfo' }  
  },
  { 
    title: '发行商', 
    dataIndex: 'publisher', 
    key: 'publisher',
    customRender: ({ text }) => text ?? '-'
  },
  { 
    title: '下载量', 
    dataIndex: 'downloads', 
    key: 'downloads',
    customRender: ({ text }) => formatNumber(text)
  },
  { 
    title: '年涨幅', 
    dataIndex: 'yearIncreaseDownloads', 
    key: 'yearIncreaseDownloads',
    // 使用trendCell插槽自定义渲染
    slots: { customRender: 'trendCell' }
  },
  { 
    title: '月涨幅', 
    dataIndex: 'monthIncreaseDownloads', 
    key: 'monthIncreaseDownloads',
    slots: { customRender: 'trendCell' }
  },
  { 
    title: '周涨幅', 
    dataIndex: 'weekIncreaseDownloads', 
    key: 'weekIncreaseDownloads',
    slots: { customRender: 'trendCell' }
  },
  { 
    title: '日涨幅', 
    dataIndex: 'dayIncreaseDownloads', 
    key: 'dayIncreaseDownloads',
    slots: { customRender: 'trendCell' }
  },
]);

// 图表
const chartRef = ref<HTMLElement | null>(null);
const heatmapRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;
let heatmap: echarts.ECharts | null = null;
const hasChartData = ref(false);

// 查询
const handleQuery = async () => {
  try {
    // 保存筛选条件
    await saveFilterSettings();

    let startTime = '';
    let endTime = '';
    if (selectedRange.value && selectedRange.value.length === 2) {
      startTime = selectedRange.value[0].format('YYYY-MM-DD');
      endTime = selectedRange.value[1].format('YYYY-MM-DD');
    }

    const params = {
      countryNames: selectedCountry.value || [],
      platformNames: selectedDevice.value || [],
      gameCategories:[],
      sortField: selectedType.value, // 'top_free' | 'top_paid' | 'top_grossing'
      startTime,
      endTime
    };

    const res = await topApi(params);
    
    const list = res || [];
    console.log('绘制应用商店排行柱状图所需数据：',list)

    if (list && list.length > 0) {
      hasChartData.value = true;
      if (chart && typeof chart.clear === 'function') {
        chart.clear();
      }
      renderChart(list);
      
      // 同时查询游戏活跃度数据
      await fetchGameActivityData();
    } else {
      hasChartData.value = false;
      if (chart && typeof chart.clear === 'function') {
        chart.clear();
      }
     
    }
  } catch (e) {
    message.error('查询失败');
    hasChartData.value = false;
    if (chart && typeof chart.clear === 'function') {
      chart.clear();
    }
    
  }
};

// 图表渲染逻辑
function renderChart(list: any[] = []) {
  if (!chart) return;

  // 1. 用索引数组做 x 轴 data，保证全是合法的字符串
  const xData = list.map((_item, idx) => idx.toString());

  // 2. 构造 rich 样式，key 用 img0、img1、… 绝对不会带点
  const richStyles = list.reduce((acc: Record<string, any>, item, idx) => {
    const styleName = `img${idx}`;
    acc[styleName] = {
      height: 28,
      width: 28,
      align: 'center',
      backgroundColor: { image: item.icon }
    };
    return acc;
  }, {});

  // 3. y 轴数据
  let yData: number[] = [];
  let yAxisName = '';
  if (selectedType.value === 'top_free' || selectedType.value === 'top_paid') {
    yData = list.map(item => item.downloads || 0);
    yAxisName = '下载量';
  } else {
    yData = list.map(item => item.revenues || 0);
    yAxisName = '收入';
  }

  chart.setOption({
    xAxis: {
      data: xData,
      axisLabel: {
        interval: 0,
        rotate: 0,
        fontSize: 12,
        margin: 20,
        // 这里的 value 就是 '0','1','2'... 对应 richStyles 里的 img0,img1...
        formatter: (value: string) => `{img${value}|}`,
        rich: richStyles
      }
    },
    yAxis: { name: yAxisName },
    series: [{ type: 'bar', data: yData, barWidth: 40, color: '#0893CF' }],
    grid: { bottom: 80 }
  });
}

// 时间选择快捷
type RangeValue = [Dayjs, Dayjs];
const rangePresets = ref([
  { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
  { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

// 游戏活跃度分析表格数据
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalGames = ref(0);

// 获取游戏活跃度数据
const fetchGameActivityData = async () => {
  try {
    // 使用应用商店排行部分的结束日期
    const endDate = selectedRange.value?.[1]?.format('YYYY-MM-DD') || dayjs().format('YYYY-MM-DD');
    
    const params = {
      date: endDate,
      page: currentPage.value,
      pageSize: pageSize.value
    };

    const res = await gameActivityAnalysisApi(params);
    console.log('获取游戏活跃度返回数据：', res)

    if (res?.games?.length > 0) {
      // 添加排名和图标字段
      const rankedGames = res.games.map((game, index) => ({
        ...game,
        ranking: index + 1 + (currentPage.value - 1) * pageSize.value,
        iconUrl: game.icon // 确保图标字段正确
      }));
      
      tableData.value = rankedGames;
      console.log('获取游戏活跃度返回数据tableData：',tableData.value)

      totalGames.value = res.totalGames;
    } else {
      tableData.value = [];
      totalGames.value = 0;
    }
  } catch (e) {
    message.error('获取游戏活跃度数据失败');
    tableData.value = [];
    totalGames.value = 0;
  }
};

// 格式化数字
function formatNumber(value: number | null | undefined): string {
  if (value === null || value === undefined) {
    return '-'; // 返回空值占位符
  }
  if (value >= 1000000) {
    return (value / 1000000).toFixed(2) + 'M';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(2) + 'K';
  } else {
    return value.toString();
  }
}

// 初始化热力图
const initHeatmap = () => {
  if (!heatmapRef.value) return;
  
  heatmap = echarts.init(heatmapRef.value);
  
  heatmap.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}'
    },
    visualMap: {
      type: 'continuous', // 使用连续型视觉映射
      min: 0,
      max: 10000000, // 根据实际数据范围调整
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
      }
    },
    series: [{
      name: '全球热度分布',
      type: 'map',
      map: 'world',
      roam: true,
      emphasis: {
        label: {
          show: true
        }
      },
      data: [] // 初始为空数组，数据将通过API获取
    }]
  });
};

// 监听窗口大小变化
window.addEventListener('resize', () => {
  chart?.resize();
  heatmap?.resize();
});

// 热力图相关状态
const selectedGame = ref(undefined);
const selectedGameInfo = ref<any>(null);
const gameOptions = ref<any[]>([]);
const gameLoading = ref(false);
const gamePage = ref(1);
const gamePageSize = ref(10);
const gameTotal = ref(0);
const gameSearchValue = ref('');

const heatmapDateRange = ref<[Dayjs, Dayjs] | null>(null);
const selectedDataType = ref('下载量');

// 添加国家名称映射
// 添加国家名称映射
// const countryNameMap: { [key: string]: string } = {
//   '泰国': 'Thailand',
//   '马来西亚': 'Malaysia',
//   '中国台湾': 'Taiwan',
//   '新加坡': 'Singapore',
//   '印度尼西亚': 'Indonesia',
//   '越南': 'Vietnam',
//   '菲律宾': 'Philippines',
//   '柬埔寨': 'Cambodia',
//   '文莱': 'Brunei',
//   '缅甸': 'Myanmar',
//   '老挝人民民主共和国': 'Laos',
//   '老挝': 'Laos',
//   '台湾-中国': 'Taiwan',
//   '大韩民国': 'South Korea',
//   '日本': 'Japan',
//   '中国': 'China'
// };
const countryNameMap = ref<Record<string,string>>({})
const manualCountryMap: Record<string, string> = {
  '中国': 'China',
  '中国台湾': 'Taiwan',
  '台湾-中国': 'Taiwan',
  '老挝人民民主共和国': 'Laos',
  '大韩民国': 'South Korea',
  '印度尼西亚': 'Indonesia',
  // ... 你需要的其他特殊国家名
};

// 游戏搜索
const onGameSearch = async (value: string) => {
  gameSearchValue.value = value;
  await loadGameOptions();
};

// 加载游戏选项
const loadGameOptions = async () => {
  gameLoading.value = true;
  try {
    const res = await findGamesByPrefixApi({
      prefix: gameSearchValue.value || '',
    });
    // 处理返回的数据
    const newGames = (res?.records || []).map((item: any) => ({
      id: item.id,
      nameZh: item.nameZh,
      iconUrl: item.iconUrl,
      // 其他需要的字段...
    }));
    // 前缀树API返回所有匹配结果，直接替换
    gameOptions.value = newGames;
    gameTotal.value = newGames.length;
  } catch (e) {
    message.error('加载游戏列表失败');
  } finally {
    gameLoading.value = false;
  }
};

// 游戏下拉框滚动加载
const onGamePopupScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 20) {
    if (gameOptions.value.length < gameTotal.value) {
      gamePage.value++;
      loadGameOptions();
    }
  }
};

// 游戏选择变化
const onGameChange = (value: string) => {
  const selectedGame = gameOptions.value.find(game => game.id === value);
  if (selectedGame) {
    selectedGameInfo.value = {
      id: selectedGame.id,
      nameZh: selectedGame.nameZh,
      iconUrl: selectedGame.iconUrl
    };
    // fetchHeatmapData();
  }
};

// 获取热力图数据
const fetchHeatmapData = async () => {
  await saveFilterSettingsHot()

  // 确保选择了游戏
  if (!selectedGame.value) {
    message.warn('请先选择游戏');
    return;
  }
  
  // 确保选择了日期范围
  if (!heatmapDateRange.value || heatmapDateRange.value.length !== 2) {
    message.warn('请选择日期范围');
    return;
  }

  try {
    const params = {
      appId: selectedGame.value,
      countryNames: selectedCountry1.value || [],
      startTime: heatmapDateRange.value[0].format('YYYY-MM-DD'),
      endTime: heatmapDateRange.value[1].format('YYYY-MM-DD'),
    };

    console.log('获取全球热度传递参数：',params)

    const response = await globalHeatApi(params);

    console.log('获取全球热度返回结果：',response)

    if (response) {
      const countryList = response.countryList || [];
      console.log('热力图国家数据：',countryList)
      
      // 根据选择的数据类型确定字段
      const dataField = selectedDataType.value === '下载量' ? 'downloads' : 'revenues';
      
      // 转换数据格式，将中文国家名称转换为英文
      // const heatmapData = countryList.map(item => ({
      //   name: countryNameMap[item.country] || item.country || 'Unknown',
      //   value: item[dataField] || 0
      // }));
      const heatmapData = countryList.map(item => ({
        name: countryNameMap.value[item.country] || item.country || 'Unknown',
        value: item[dataField] || 0
      }))

      let chinaValue = 0;
      const mergedMap = new Map();
      for (const item of heatmapData) {
        if (item.name === 'Taiwan') {
          chinaValue += Number(item.value);
          continue;
        }
        if (mergedMap.has(item.name)) {
          mergedMap.set(item.name, mergedMap.get(item.name) + Number(item.value));
        } else {
          mergedMap.set(item.name, Number(item.value));
        }
      }
      if (chinaValue > 0) {
        mergedMap.set('China', (mergedMap.get('China') || 0) + chinaValue);
      }
      const mergedHeatmapData = Array.from(mergedMap.entries()).map(([name, value]) => ({ name, value }));


      console.log('热力图数据：',mergedHeatmapData)
      
      // 更新热力图数据
      if (heatmapRef.value) {
        const chart = echarts.getInstanceByDom(heatmapRef.value);
        if (chart) {
          chart.setOption({
            series: [{
              data: mergedHeatmapData
            }]
          });
        }
      }
    }
  } catch (error) {
    console.error('获取热力图数据失败:', error);
  }
};

// 添加标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { class: 'ellipsis-tag' }, '...');
};

// 添加选择全部和处理函数
const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    // 如果只选了all，表示全选；如果all和其他一起选，表示清空
    if (value.length === 1) {
      selectedCountry.value = countries.value.map(c => c.value);
    } else {
      selectedCountry.value = [];
    }
  } else {
    selectedCountry.value = value;
  }
};

const handleCountryChange1 = (value: string[]) => {
  if (value.includes('all')) {
    // 如果只选了all，表示全选；如果all和其他一起选，表示清空
    if (value.length === 1) {
      selectedCountry1.value = countries.value.map(c => c.value);
    } else {
      selectedCountry1.value = [];
    }
  } else {
    selectedCountry1.value = value;
  }
};

const handleDeviceChange = (value: string[]) => {
  if (value.includes('all')) {
    if (value.length === 1) {
      selectedDevice.value = devices.value.map(d => d.value);
    } else {
      selectedDevice.value = [];
    }
  } else {
    selectedDevice.value = value;
  }
};

const userStore = useUserStore();
const redisKey = userStore.userInfo?.username || String(userStore.userInfo?.id);
const fieldName = '商店与渠道表现';

// 保存筛选条件
const saveFilterSettings = async () => {
  try {
    const filterData = {
      selectedType: selectedType.value || null,
      selectedRange: selectedRange.value ? [selectedRange.value[0].format('YYYY-MM-DD'), selectedRange.value[1].format('YYYY-MM-DD')] : [],
      selectedCountry: selectedCountry.value || null,
      selectedDevice: selectedDevice.value || null
    };
    
    const params = {
      redisKey: redisKey,
      field: fieldName,
      value: filterData
    }
    await saveFilterSettingsApi(params);
  } catch (e) {
    console.error('保存筛选条件失败', e);
  }
};

// 恢复筛选条件
const restoreFilterSettings = async () => {
  try {
    const res = await getFilterSettingsApi({
      redisKey: redisKey,
      field: fieldName
    });
    
    if (res) {
      const filterData = res[fieldName];
      
      // 恢复选中的类型
      if (filterData.selectedType) {
        selectedType.value = filterData.selectedType || null;
      }
      
      // 恢复日期范围
      if (filterData.selectedRange && filterData.selectedRange.length === 2) {
        selectedRange.value = [dayjs(filterData.selectedRange[0]), dayjs(filterData.selectedRange[1]) || null];
      }
      
      // 恢复国家
      selectedCountry.value = filterData.selectedCountry || null;
      
      // 恢复平台
      selectedDevice.value = filterData.selectedDevice || null;
      
      return true;
    }
  } catch (e) {
    console.error('恢复筛选条件失败', e);
  }
  return false;
};

const fieldNameHot = '商店与渠道表现-热力图';

// 保存筛选条件
const saveFilterSettingsHot = async () => {
  try {
    const filterData = {
      appId: selectedGame.value || null,
      heatmapDateRange: heatmapDateRange.value ? [heatmapDateRange.value[0].format('YYYY-MM-DD'), heatmapDateRange.value[1].format('YYYY-MM-DD')] : null,
      selectedCountry1: selectedCountry1.value || null,
      selectedDataType: selectedDataType.value || null
    };
    
    const params = {
      redisKey: redisKey,
      field: fieldNameHot,
      value: filterData
    }
    console.log('保存筛选条件参数：',params)
    
    await saveFilterSettingsApi(params);
  } catch (e) {
    console.error('保存筛选条件失败', e);
  }
};

// 恢复筛选条件
const restoreFilterSettingsHot = async () => {
  try {
    const res = await getFilterSettingsApi({
      redisKey: redisKey,
      field: fieldNameHot
    });

    console.log('获取筛选条件结果：',res)
    
    if (res) {
      const filterData = res[fieldNameHot];

      if(filterData.appId){
        selectedGame.value = filterData.appId;
      }
      
      // 恢复选中的类型
      if (filterData.selectedDataType) {
        selectedDataType.value = filterData.selectedDataType || null;
      }
      
      // 恢复日期范围
      if (filterData.heatmapDateRange && filterData.heatmapDateRange.length === 2) {
        heatmapDateRange.value = [dayjs(filterData.heatmapDateRange[0]), dayjs(filterData.heatmapDateRange[1]) || null];
      }
      
      // 恢复国家
      selectedCountry1.value = filterData.selectedCountry1 || null;
      
      return true;
    }
  } catch (e) {
    console.error('恢复筛选条件失败', e);
  }
  return false;
};

</script>

<style lang="scss" scoped>
.chart-container {
  border: 5px;
  margin: 10px 10px;
  height: auto;
  min-height: 400px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  overflow: visible;
  box-shadow:
    0px 0px 0px rgba(77, 85, 117, 0.05),
    0px 3px 7px rgba(77, 85, 117, 0.05),
    0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03),
    0px 20px 20px rgba(77, 85, 117, 0.01),
    0px 35px 30px rgba(77, 85, 117, 0);
}
.table-container {
  margin: 24px 2% 0 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 24px;
}

.custom-table {
  width: 100%;
  font-size: 14px;
}

/* 表头样式 */
:deep(.custom-table .ant-table-thead > tr > th) {
  background-color: #c2e8f8;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 12px 16px;
}

/* 表格单元格样式 */
:deep(.custom-table .ant-table-tbody > tr > td) {
  padding: 12px 16px;
  text-align: center;
  color: #666;
  vertical-align: middle;
  border-bottom: 1px solid #eee;
}

/* 斑马纹样式 */
:deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
  background-color: #ffffff;
}
:deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
  background-color: #dcf2fb;
}

/* 悬停样式 */
:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

.chart-container1 {
    display: flex;
    /* 使用 flex 布局 */
    gap: 10px;
    /* 设置元素之间的间距 */
}

.chart-container h2 {
    border: 3px solid #0893CF;
    /* 2px 宽度的 #0893CF 色边框 */
    border-top: none;
    /* 取消上边框 */
    border-right: none;
    /* 取消右边框 */
    border-bottom: none;
    /* 取消下边框 */
    padding-left: 10px;
    /* 可选：左边内边距，使文字与左边框有一定距离 */
    margin-bottom: 20px;
    /* 可选：下边距，使标题与图表有一定距离 */
}

.custom-button {
    border: 2px solid #ccc;
    /* 未选中时的灰色边框 */
    color: #333;
    /* 未选中时的字体颜色 */
    transition: border-color 0.3s, color 0.3s;
    /* 添加过渡效果 */
}

.custom-button.selected {
    border-color: #0893CF;
    /* 选中时的边框颜色 */
    color: #0893CF;
    /* 选中时的字体颜色 */
}

:deep(.ant-table-title) {
    margin-top: -40px;
}
.empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.empty-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 10px;
}

.empty-data-container p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.empty-data-tip {
  font-size: 14px;
  color: #999;
}
</style>

